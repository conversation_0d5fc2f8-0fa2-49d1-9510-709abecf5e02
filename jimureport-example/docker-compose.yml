version: '2'
services:
  jimureport-mysql:
    build:
      context: ./db
    environment:
      MYSQL_ROOT_PASSWORD: root
    restart: always
    container_name: jimureport-mysql
    image: jimureport-mysql
    command:
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --explicit_defaults_for_timestamp=true
      --lower_case_table_names=1
      --max_allowed_packet=128M
    ports:
      - 3307:3306

  jimureport:
    build:
      context: .
    restart: always
    container_name: jimureport
    image: jimureport
    ports:
      - 8085:8085