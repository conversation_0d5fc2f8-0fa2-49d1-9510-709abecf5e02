<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业信息管理 - 积木报表</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/4.6.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .enterprise-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 20px;
        }
        .enterprise-name {
            font-size: 1.2em;
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
        }
        .enterprise-info {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-building"></i> 企业信息管理</h1>
                    <p class="mb-0">管理企业信息，查看企业填报报表</p>
                </div>
                <div class="col-md-6 text-right">
                    <a href="/jmreport/list" class="btn btn-outline-light">
                        <i class="fas fa-chart-bar"></i> 报表工作台
                    </a>
                    <a href="/drag/list" class="btn btn-outline-light ml-2">
                        <i class="fas fa-tachometer-alt"></i> 仪表盘工作台
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <div class="col-12">
                <h3>企业列表</h3>
                <p class="text-muted">以下是系统中的企业信息（模拟数据）</p>
            </div>
        </div>

        <div class="row">
            <!-- 模拟企业数据 -->
            <div class="col-md-6 col-lg-4">
                <div class="enterprise-card">
                    <div class="enterprise-name">测试企业001</div>
                    <div class="enterprise-info">
                        <p><strong>企业代码：</strong>CODE001</p>
                        <p><strong>所属行业：</strong>制造业</p>
                        <p><strong>联系人：</strong>张三</p>
                        <p><strong>联系电话：</strong>13800138001</p>
                        <p><strong>报表数量：</strong>5 个</p>
                    </div>
                    <div class="mt-3">
                        <a href="/enterprise/reports?enterpriseId=ENT001" class="btn btn-primary btn-sm">
                            <i class="fas fa-eye"></i> 查看报表
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-4">
                <div class="enterprise-card">
                    <div class="enterprise-name">测试企业002</div>
                    <div class="enterprise-info">
                        <p><strong>企业代码：</strong>CODE002</p>
                        <p><strong>所属行业：</strong>服务业</p>
                        <p><strong>联系人：</strong>李四</p>
                        <p><strong>联系电话：</strong>13800138002</p>
                        <p><strong>报表数量：</strong>3 个</p>
                    </div>
                    <div class="mt-3">
                        <a href="/enterprise/reports?enterpriseId=ENT002" class="btn btn-primary btn-sm">
                            <i class="fas fa-eye"></i> 查看报表
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-4">
                <div class="enterprise-card">
                    <div class="enterprise-name">测试企业003</div>
                    <div class="enterprise-info">
                        <p><strong>企业代码：</strong>CODE003</p>
                        <p><strong>所属行业：</strong>建筑业</p>
                        <p><strong>联系人：</strong>王五</p>
                        <p><strong>联系电话：</strong>13800138003</p>
                        <p><strong>报表数量：</strong>8 个</p>
                    </div>
                    <div class="mt-3">
                        <a href="/enterprise/reports?enterpriseId=ENT003" class="btn btn-primary btn-sm">
                            <i class="fas fa-eye"></i> 查看报表
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12 text-center">
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle"></i> 功能说明</h5>
                    <p class="mb-0">
                        这是一个简化的企业管理页面。点击"查看报表"可以查看对应企业的报表列表。
                        <br>
                        完整功能包括：企业信息搜索、分页显示、动态加载等。
                    </p>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-12 text-center">
                <a href="/enterprise/test" class="btn btn-secondary mr-2">
                    <i class="fas fa-cog"></i> Freemarker测试
                </a>
                <a href="/jmreport/list" class="btn btn-success">
                    <i class="fas fa-chart-bar"></i> 访问报表工作台
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/4.6.2/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            console.log('企业管理页面加载完成');
            
            // 简单的点击统计
            $('.enterprise-card').on('click', function() {
                console.log('点击了企业卡片');
            });
        });
    </script>
</body>
</html>
