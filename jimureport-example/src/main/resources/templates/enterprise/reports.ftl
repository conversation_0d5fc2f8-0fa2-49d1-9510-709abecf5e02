<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业报表列表 - 积木报表</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/4.6.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .breadcrumb-custom {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 15px 20px;
            margin-bottom: 20px;
        }
        .filter-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .report-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .report-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        .report-header {
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .report-title {
            font-size: 1.1em;
            font-weight: bold;
            color: #495057;
            margin: 0;
        }
        .report-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 500;
        }
        .status-0 { background-color: #f8d7da; color: #721c24; }
        .status-1 { background-color: #d4edda; color: #155724; }
        .status-2 { background-color: #cce7ff; color: #004085; }
        .status-3 { background-color: #fff3cd; color: #856404; }
        .report-body {
            padding: 20px;
        }
        .report-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f1f1f1;
        }
        .info-label {
            color: #6c757d;
            font-weight: 500;
        }
        .info-value {
            color: #495057;
            font-weight: 600;
        }
        .progress-bar-custom {
            height: 8px;
            border-radius: 4px;
            background-color: #e9ecef;
            overflow: hidden;
            margin-top: 5px;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s ease;
        }
        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .btn-action {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            border: none;
            transition: all 0.2s;
        }
        .btn-view {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .btn-view:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            color: white;
            transform: scale(1.05);
        }
        .btn-download {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        .btn-download:hover {
            background: linear-gradient(135deg, #218838 0%, #1ea080 100%);
            color: white;
            transform: scale(1.05);
        }
        .btn-edit {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
        }
        .btn-edit:hover {
            background: linear-gradient(135deg, #e0a800 0%, #e8590c 100%);
            color: white;
            transform: scale(1.05);
        }
        .no-data {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        .loading {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        .enterprise-info-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .enterprise-name {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .enterprise-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .detail-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .detail-icon {
            width: 16px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-file-alt"></i> 企业报表管理</h1>
                    <p class="mb-0">查看和管理企业填报的各类报表</p>
                </div>
                <div class="col-md-4 text-right">
                    <a href="/enterprise/index" class="btn btn-outline-light">
                        <i class="fas fa-arrow-left"></i> 返回企业列表
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 面包屑导航 -->
        <div class="breadcrumb-custom">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="/enterprise/index">企业管理</a></li>
                    <li class="breadcrumb-item active" aria-current="page" id="enterpriseName">企业报表</li>
                </ol>
            </nav>
        </div>

        <!-- 企业信息卡片 -->
        <div class="enterprise-info-card" id="enterpriseInfo">
            <div class="loading">
                <i class="fas fa-spinner fa-spin"></i> 正在加载企业信息...
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="filter-card">
            <form id="filterForm">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="reportType">报表类型</label>
                            <select class="form-control" id="reportType" name="reportType">
                                <option value="">全部类型</option>
                                <option value="财务报表">财务报表</option>
                                <option value="统计报表">统计报表</option>
                                <option value="税务报表">税务报表</option>
                                <option value="其他报表">其他报表</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="reportStatus">填报状态</label>
                            <select class="form-control" id="reportStatus" name="reportStatus">
                                <option value="">全部状态</option>
                                <option value="0">未填报</option>
                                <option value="1">已填报</option>
                                <option value="2">已审核</option>
                                <option value="3">已驳回</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="reportYear">报告年度</label>
                            <select class="form-control" id="reportYear" name="reportYear">
                                <option value="">全部年度</option>
                                <option value="2024">2024年</option>
                                <option value="2023">2023年</option>
                                <option value="2022">2022年</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="reportName">报表名称</label>
                            <input type="text" class="form-control" id="reportName" name="reportName" placeholder="请输入报表名称">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 text-center">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> 筛选
                        </button>
                        <button type="button" class="btn btn-secondary ml-2" onclick="resetFilter()">
                            <i class="fas fa-redo"></i> 重置
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- 报表列表 -->
        <div id="reportsList">
            <div class="loading">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p class="mt-3">正在加载报表信息...</p>
            </div>
        </div>

        <!-- 分页区域 -->
        <div class="d-flex justify-content-center mt-4">
            <nav aria-label="报表列表分页">
                <ul class="pagination" id="pagination">
                    <!-- 分页内容将通过JavaScript动态生成 -->
                </ul>
            </nav>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/4.6.2/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentPage = 1;
        let pageSize = 10;
        let enterpriseId = '';

        // 页面加载完成后初始化
        $(document).ready(function() {
            // 从URL参数获取企业ID
            const urlParams = new URLSearchParams(window.location.search);
            enterpriseId = urlParams.get('enterpriseId');
            
            if (!enterpriseId) {
                showError('缺少企业ID参数');
                return;
            }
            
            // 加载企业信息和报表列表
            loadEnterpriseInfo();
            loadReports(1);
            
            // 筛选表单提交
            $('#filterForm').on('submit', function(e) {
                e.preventDefault();
                currentPage = 1;
                loadReports(1);
            });
        });

        // 加载企业信息
        function loadEnterpriseInfo() {
            $.ajax({
                url: '/enterprise/detail/' + enterpriseId,
                type: 'GET',
                success: function(response) {
                    if (response.success) {
                        renderEnterpriseInfo(response.data);
                    } else {
                        $('#enterpriseInfo').html(`
                            <div class="text-center text-danger">
                                <i class="fas fa-exclamation-triangle"></i> 加载企业信息失败：${response.message}
                            </div>
                        `);
                    }
                },
                error: function() {
                    $('#enterpriseInfo').html(`
                        <div class="text-center text-danger">
                            <i class="fas fa-exclamation-triangle"></i> 网络错误，请稍后重试
                        </div>
                    `);
                }
            });
        }

        // 渲染企业信息
        function renderEnterpriseInfo(enterprise) {
            $('#enterpriseName').text(enterprise.name + ' - 报表列表');
            
            const html = `
                <div class="enterprise-name">${enterprise.name}</div>
                <div class="enterprise-details">
                    <div class="detail-item">
                        <i class="fas fa-code detail-icon"></i>
                        <span>企业代码：${enterprise.code}</span>
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-industry detail-icon"></i>
                        <span>所属行业：${enterprise.industry || '未设置'}</span>
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-user detail-icon"></i>
                        <span>联系人：${enterprise.contact || '未设置'}</span>
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-phone detail-icon"></i>
                        <span>联系电话：${enterprise.phone || '未设置'}</span>
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-file-alt detail-icon"></i>
                        <span>报表数量：${enterprise.reportCount || 0} 个</span>
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-clock detail-icon"></i>
                        <span>最后填报：${enterprise.lastReportTime || '暂无'}</span>
                    </div>
                </div>
            `;
            
            $('#enterpriseInfo').html(html);
        }

        // 加载报表列表
        function loadReports(page) {
            currentPage = page;
            const formData = $('#filterForm').serialize();
            
            $.ajax({
                url: '/enterprise/reports/' + enterpriseId,
                type: 'GET',
                data: formData + '&page=' + page + '&size=' + pageSize,
                success: function(response) {
                    if (response.success) {
                        renderReportsList(response.data);
                        // 这里应该有分页信息，暂时模拟
                        renderPagination(1, 1, response.data.length);
                    } else {
                        showError('加载报表列表失败：' + response.message);
                    }
                },
                error: function() {
                    showError('网络错误，请稍后重试');
                }
            });
        }

        // 渲染报表列表
        function renderReportsList(reports) {
            const listContainer = $('#reportsList');
            
            if (!reports || reports.length === 0) {
                listContainer.html(`
                    <div class="no-data">
                        <i class="fas fa-inbox fa-3x"></i>
                        <p class="mt-3">暂无报表信息</p>
                    </div>
                `);
                return;
            }

            let html = '';
            reports.forEach(function(report) {
                const statusText = getStatusText(report.status);
                const statusClass = 'status-' + report.status;
                
                html += `
                    <div class="report-card">
                        <div class="report-header">
                            <h5 class="report-title">${report.name}</h5>
                            <span class="report-status ${statusClass}">${statusText}</span>
                        </div>
                        <div class="report-body">
                            <div class="report-info">
                                <div class="info-item">
                                    <span class="info-label">报表类型：</span>
                                    <span class="info-value">${report.type || '未设置'}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">报告期：</span>
                                    <span class="info-value">${report.reportPeriod || '未设置'}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">填报人：</span>
                                    <span class="info-value">${report.reportByName || '未设置'}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">创建时间：</span>
                                    <span class="info-value">${formatDate(report.createTime)}</span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="info-label">填报进度：</span>
                                    <span class="info-value">${report.progress || 0}%</span>
                                </div>
                                <div class="progress-bar-custom">
                                    <div class="progress-fill" style="width: ${report.progress || 0}%"></div>
                                </div>
                            </div>
                            <div class="action-buttons">
                                <button class="btn btn-action btn-view" onclick="viewReport('${report.id}')">
                                    <i class="fas fa-eye"></i> 查看报表
                                </button>
                                <button class="btn btn-action btn-download" onclick="downloadReport('${report.id}')">
                                    <i class="fas fa-download"></i> 导出Excel
                                </button>
                                ${report.status === 0 || report.status === 3 ? `
                                <button class="btn btn-action btn-edit" onclick="editReport('${report.id}')">
                                    <i class="fas fa-edit"></i> 填报
                                </button>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                `;
            });
            
            listContainer.html(html);
        }

        // 渲染分页
        function renderPagination(current, pages, total) {
            // 简化的分页实现
            $('#pagination').html('');
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                0: '未填报',
                1: '已填报',
                2: '已审核',
                3: '已驳回'
            };
            return statusMap[status] || '未知状态';
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '未设置';
            const date = new Date(dateStr);
            return date.toLocaleDateString('zh-CN');
        }

        // 查看报表
        function viewReport(reportId) {
            window.open('/jmreport/view/' + reportId, '_blank');
        }

        // 下载报表
        function downloadReport(reportId) {
            window.location.href = '/jmreport/exportExcel/' + reportId;
        }

        // 编辑报表
        function editReport(reportId) {
            window.open('/jmreport/edit/' + reportId, '_blank');
        }

        // 重置筛选
        function resetFilter() {
            $('#filterForm')[0].reset();
            currentPage = 1;
            loadReports(1);
        }

        // 显示错误信息
        function showError(message) {
            $('#reportsList').html(`
                <div class="text-center text-danger">
                    <i class="fas fa-exclamation-triangle fa-3x"></i>
                    <p class="mt-3">${message}</p>
                </div>
            `);
        }
    </script>
</body>
</html>
