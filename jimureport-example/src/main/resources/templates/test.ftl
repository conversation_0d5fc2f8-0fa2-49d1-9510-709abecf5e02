<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Freemarker测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 50px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            color: #28a745;
            font-size: 18px;
            font-weight: bold;
        }
        .info {
            color: #6c757d;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Freemarker配置测试</h1>
        <div class="success">✓ Freemarker模板引擎工作正常！</div>
        
        <div class="info">
            <h3>测试信息：</h3>
            <p>当前时间：${.now}</p>
            <p>模板名称：${.template_name}</p>
            <p>测试变量：${title!'默认标题'}</p>
        </div>
        
        <div class="info">
            <h3>语法测试：</h3>
            <p>空值处理：${nonExistentVar!'这是默认值'}</p>
            <p>数字默认值：${nonExistentNumber!0}</p>
            <p>布尔默认值：${nonExistentBoolean?c}</p>
        </div>
        
        <a href="/enterprise/index" style="display: inline-block; margin-top: 20px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;">
            前往企业管理页面
        </a>
    </div>
</body>
</html>
