server:
  port: 8085
spring:
  #数据库
  datasource:
#    url: jdbc:mysql://${MYSQL-HOST:127.0.0.1}:${MYSQL-PORT:3306}/${MYSQL-DB:jimureport}?characterEncoding=UTF-8&useUnicode=true&useSSL=false&tinyInt1isBit=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
    url: jdbc:dm://*************:5236/HdbSurvey?timezone=GMT+8
    username: HdbSurvey
    password: Metabc123
    driver-class-name: dm.jdbc.driver.DmDriver
  security:
    #放开预览页面不需要登录
    open-view-page: true
    #登录账号和密码
    user:
      name: "admin"
      password: "123456"
  #配置freemarker
  freemarker:
    # 设置模板后缀名
    suffix: .ftl
    # 设置文档类型
    content-type: text/html
    # 设置页面编码格式
    charset: UTF-8
    # 设置页面缓存
    cache: false
    prefer-file-system-access: false
    # 设置ftl文件路径
    template-loader-path:
      - classpath:/templates
    template_update_delay: 0
#持久层框架
minidao:
  base-package: org.jeecg.modules.jmreport.*,org.jeecg.modules.drag.*
jeecg:
  # local|minio|alioss
  uploadType: local
  # local
  path:
    #文件路径
    upload: /opt/upload
  # alioss
  oss:
    endpoint: ??
    accessKey: ??
    secretKey: ??
    bucketName: jimureport
  # minio
  minio:
    minio_url: http://minio.jeecg.com
    minio_name: ??
    minio_pass: ??
    bucketName: ??
  #大屏报表参数设置
  jmreport:
    #多租户模式，默认值为空(created:按照创建人隔离、tenant:按照租户隔离)
    saasMode:
    # 平台上线安全配置
    firewall:
      # 数据源安全 (开启后，不允许使用平台数据源、SQL解析加签并且不允许查询数据库)
      dataSourceSafe: false
      # 低代码开发模式（dev:开发模式，prod:发布模式—关闭在线报表设计功能，分配角色admin、lowdeveloper可放开限制）
      lowCodeMode: dev
    # 展示列数
    col: 100
    # 展示行数
    row: 200
    #自定义API接口的前缀 #{api_base_path}和#{domainURL}的值
    apiBasePath: http://************:8085
    pageSize:
      - 10
      - 20
      - 30
      - 40
    # 邮件发送
    mail:
      enabled: false
      sender: 积木报表
      host: smtp.exmail.qq.com
      username: ??
      password: ??
#输出sql日志
logging:
  level:
    org.jeecg.modules.jmreport: info
