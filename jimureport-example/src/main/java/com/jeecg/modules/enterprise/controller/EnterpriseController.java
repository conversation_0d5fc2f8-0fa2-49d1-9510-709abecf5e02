package com.jeecg.modules.enterprise.controller;

import com.jeecg.modules.enterprise.entity.Enterprise;
import com.jeecg.modules.enterprise.entity.EnterpriseReport;
import com.jeecg.modules.enterprise.service.IEnterpriseService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 企业信息管理控制器
 * 提供企业信息列表查询、搜索、分页等功能
 * 
 * <AUTHOR>
 * @date 2024-12-11
 */
@Slf4j
@Controller
@RequestMapping("/enterprise")
public class EnterpriseController {

    @Autowired
    private IEnterpriseService enterpriseService;

    /**
     * 企业信息管理主页面
     */
    @GetMapping("/index")
    public String index(Model model) {
        model.addAttribute("title", "企业信息管理");
        return "enterprise/list";
    }

    /**
     * 企业报表列表页面
     */
    @GetMapping("/reports")
    public String reports(@RequestParam String enterpriseId, Model model) {
        model.addAttribute("enterpriseId", enterpriseId);
        model.addAttribute("title", "企业报表管理");
        return "enterprise/reports";
    }

    /**
     * Freemarker测试页面
     */
    @GetMapping("/test")
    public String test(Model model) {
        model.addAttribute("title", "Freemarker测试成功");
        return "test";
    }

    /**
     * 简化版企业信息管理页面
     */
    @GetMapping("/simple")
    public String simple(Model model) {
        model.addAttribute("title", "企业信息管理（简化版）");
        return "enterprise/simple-list";
    }

    /**
     * 获取企业信息列表（分页查询）
     * 
     * @param page 页码
     * @param size 每页大小
     * @param enterpriseName 企业名称（模糊查询）
     * @param enterpriseCode 企业代码（精确查询）
     * @param industry 所属行业
     * @return 分页结果
     */
    @GetMapping("/list")
    @ResponseBody
    public Map<String, Object> getEnterpriseList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String enterpriseName,
            @RequestParam(required = false) String enterpriseCode,
            @RequestParam(required = false) String industry) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 设置分页参数
            PageHelper.startPage(page, size);
            
            // 构建查询条件
            Map<String, Object> params = new HashMap<>();
            if (enterpriseName != null && !enterpriseName.trim().isEmpty()) {
                params.put("enterpriseName", enterpriseName.trim());
            }
            if (enterpriseCode != null && !enterpriseCode.trim().isEmpty()) {
                params.put("enterpriseCode", enterpriseCode.trim());
            }
            if (industry != null && !industry.trim().isEmpty()) {
                params.put("industry", industry.trim());
            }
            
            // 查询企业列表
            List<Enterprise> enterprises = enterpriseService.getEnterpriseList(params);
            
            // 获取分页信息
            PageInfo<Enterprise> pageInfo = new PageInfo<>(enterprises);
            
            // 构建返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("records", pageInfo.getList());
            data.put("total", pageInfo.getTotal());
            data.put("current", pageInfo.getPageNum());
            data.put("pages", pageInfo.getPages());
            data.put("size", pageInfo.getPageSize());
            
            result.put("success", true);
            result.put("data", data);
            result.put("message", "查询成功");
            
            log.info("企业列表查询成功，页码：{}，每页大小：{}，总记录数：{}", page, size, pageInfo.getTotal());
            
        } catch (Exception e) {
            log.error("查询企业列表失败", e);
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取企业报表列表
     * 
     * @param enterpriseId 企业ID
     * @return 企业报表列表
     */
    @GetMapping("/reports/{enterpriseId}")
    @ResponseBody
    public Map<String, Object> getEnterpriseReports(@PathVariable String enterpriseId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 查询企业报表列表
            List<EnterpriseReport> reports = enterpriseService.getEnterpriseReports(enterpriseId);
            
            result.put("success", true);
            result.put("data", reports);
            result.put("message", "查询成功");
            
            log.info("企业报表列表查询成功，企业ID：{}，报表数量：{}", enterpriseId, reports.size());
            
        } catch (Exception e) {
            log.error("查询企业报表列表失败，企业ID：{}", enterpriseId, e);
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取企业详细信息
     * 
     * @param enterpriseId 企业ID
     * @return 企业详细信息
     */
    @GetMapping("/detail/{enterpriseId}")
    @ResponseBody
    public Map<String, Object> getEnterpriseDetail(@PathVariable String enterpriseId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Enterprise enterprise = enterpriseService.getEnterpriseById(enterpriseId);
            
            if (enterprise != null) {
                result.put("success", true);
                result.put("data", enterprise);
                result.put("message", "查询成功");
            } else {
                result.put("success", false);
                result.put("message", "企业不存在");
            }
            
            log.info("企业详情查询，企业ID：{}，结果：{}", enterpriseId, enterprise != null ? "成功" : "企业不存在");
            
        } catch (Exception e) {
            log.error("查询企业详情失败，企业ID：{}", enterpriseId, e);
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取行业列表
     * 
     * @return 行业列表
     */
    @GetMapping("/industries")
    @ResponseBody
    public Map<String, Object> getIndustries() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<String> industries = enterpriseService.getIndustries();
            
            result.put("success", true);
            result.put("data", industries);
            result.put("message", "查询成功");
            
        } catch (Exception e) {
            log.error("查询行业列表失败", e);
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 搜索企业（自动完成）
     * 
     * @param keyword 关键词
     * @param limit 限制数量
     * @return 企业列表
     */
    @GetMapping("/search")
    @ResponseBody
    public Map<String, Object> searchEnterprises(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "10") Integer limit) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<Enterprise> enterprises = enterpriseService.searchEnterprises(keyword, limit);
            
            result.put("success", true);
            result.put("data", enterprises);
            result.put("message", "搜索成功");
            
        } catch (Exception e) {
            log.error("搜索企业失败，关键词：{}", keyword, e);
            result.put("success", false);
            result.put("message", "搜索失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取企业统计信息
     * 
     * @return 统计信息
     */
    @GetMapping("/statistics")
    @ResponseBody
    public Map<String, Object> getStatistics() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> statistics = enterpriseService.getStatistics();
            
            result.put("success", true);
            result.put("data", statistics);
            result.put("message", "查询成功");
            
        } catch (Exception e) {
            log.error("查询企业统计信息失败", e);
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        
        return result;
    }
}
