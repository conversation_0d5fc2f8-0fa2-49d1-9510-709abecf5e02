package com.jeecg.modules.enterprise.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业信息实体类
 * 
 * <AUTHOR>
 * @date 2024-12-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class Enterprise implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 企业ID
     */
    private String id;

    /**
     * 企业名称
     */
    private String name;

    /**
     * 企业代码
     */
    private String code;

    /**
     * 统一社会信用代码
     */
    private String creditCode;

    /**
     * 所属行业
     */
    private String industry;

    /**
     * 企业类型
     */
    private String type;

    /**
     * 企业规模
     */
    private String scale;

    /**
     * 注册资本
     */
    private String registeredCapital;

    /**
     * 成立日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date establishDate;

    /**
     * 法定代表人
     */
    private String legalRepresentative;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 联系邮箱
     */
    private String email;

    /**
     * 传真号码
     */
    private String fax;

    /**
     * 企业地址
     */
    private String address;

    /**
     * 邮政编码
     */
    private String zipCode;

    /**
     * 企业网站
     */
    private String website;

    /**
     * 企业简介
     */
    private String description;

    /**
     * 营业执照号
     */
    private String businessLicense;

    /**
     * 税务登记号
     */
    private String taxRegistration;

    /**
     * 组织机构代码
     */
    private String organizationCode;

    /**
     * 企业状态（1：正常，0：停用）
     */
    private Integer status;

    /**
     * 报表数量
     */
    private Integer reportCount;

    /**
     * 最后填报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastReportTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0：正常，1：删除）
     */
    private Integer delFlag;

    /**
     * 版本号
     */
    private Integer version;
}
